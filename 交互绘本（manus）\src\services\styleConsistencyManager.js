// LiblibAI专用风格一致性管理器
// 确保绘本插画的风格和人物形象保持高度一致
// 支持多主题绘本系统

import {
  generateUniversalPrompt,
  getCharactersByTheme,
  getArtStyleByTheme,
  validateThemeAndCharacters,
  STORYBOOK_THEMES
} from './universalStyleManager.js';

/**
 * 详细的角色外观规范 - 专为LiblibAI优化
 */
export const CHARACTER_SPECIFICATIONS = {
  bobo: {
    name: "小熊波波 (<PERSON> Bobo)",
    description: `
棕色小熊，温暖友善的主角：
- 毛色：焦糖棕色 (#D2691E)，毛发蓬松但整齐，有自然光泽
- 脸部：完美圆形，直径为身高的1/4，表情永远温和友善
- 鼻子：黑色圆形小鼻子，位于脸部正中央，大小为脸部宽度的1/8
- 眼睛：大而圆的黑色眼睛，眼白清晰，瞳孔居中，眼睛间距等于一个眼睛宽度
- 耳朵：小圆耳朵，位于头顶两侧45度角，内侧浅棕色 (#DEB887)
- 身材：略圆润体型，身高为画面高度的1/3，比例协调可爱
- 服装：鲜红色 (#DC143C) 圆领短袖T恤 + 深蓝色 (#191970) 短裤
- 姿态：站立时微前倾，双臂自然下垂或做友好手势`,
    colors: ["#D2691E", "#DEB887", "#DC143C", "#191970", "#000000"]
  },

  lili: {
    name: "小兔莉莉 (Rabbit Lily)",
    description: `
优雅的白兔，温柔的女性角色：
- 毛色：珍珠白 (#F8F8FF) 主体，耳朵尖端浅灰色 (#D3D3D3)
- 脸部：椭圆形，比波波小15%，轮廓柔和优雅
- 鼻子：粉红色 (#FFB6C1) 三角形小鼻子，位于脸部中央偏下
- 眼睛：温柔的黑色眼睛，比波波小20%，眼神柔和慈祥
- 耳朵：长耳朵，长度为身高的1/3，一只竖立一只微垂，内侧粉色
- 身材：纤细优雅，身高比波波小10%，动作轻盈
- 服装：粉红色 (#FFB6C1) 连衣裙，偶尔戴黄色 (#FFD700) 小花发饰
- 姿态：优雅端庄，动作轻柔，经常做关怀手势`,
    colors: ["#F8F8FF", "#D3D3D3", "#FFB6C1", "#FFD700", "#000000"]
  },

  teacher: {
    name: "乌龟老师 (Turtle Teacher)",
    description: `
智慧的乌龟，权威的教育者形象：
- 外观：头部四肢橄榄绿 (#808000)，龟壳深棕色 (#8B4513)
- 脸部：方形脸，表情和蔼，有智慧皱纹
- 眼睛：戴金色 (#FFD700) 圆框眼镜，眼神智慧慈祥
- 特征：龟壳六边形花纹清晰，脖子适中长度
- 身材：稳重体型，坐立时非常稳定，给人可靠感
- 配饰：金色圆框眼镜，常持书本或教具
- 姿态：沉稳可靠，动作缓慢有条理，指导性手势`,
    colors: ["#808000", "#8B4513", "#FFD700", "#000000"]
  },

  squirrels: {
    name: "松鼠兄弟 (Squirrel Brothers)",
    description: `
活泼的松鼠双胞胎，充满活力：
- 毛色：红棕色 (#A0522D)，毛发蓬松有光泽
- 脸部：尖脸，黑色小鼻子，表情机灵活泼
- 眼睛：明亮的黑色眼睛，眼神机灵，闪烁好奇光芒
- 尾巴：大蓬松尾巴，占身长2/3，常呈问号形
- 身材：小巧灵活，身高为波波的2/3，动作敏捷
- 服装：哥哥黄色 (#FFD700) 背心，弟弟绿色 (#32CD32) 背心
- 姿态：活泼好动，跳跃攀爬，表情丰富多变`,
    colors: ["#A0522D", "#FFD700", "#32CD32", "#000000"]
  }
};

/**
 * 艺术风格规范 - LiblibAI专用
 */
export const ARTISTIC_STYLE_GUIDE = {
  technique: "温暖水彩画风格，轻微纸张纹理，边缘柔和",
  lineStyle: "深棕色 (#654321) 轮廓线，2-3px宽度，圆润连接",
  colorPalette: {
    primary: ["#D2691E", "#CD853F", "#DEB887"], // 棕色系
    secondary: ["#90EE90", "#98FB98", "#87CEEB", "#B0E0E6"], // 绿蓝系
    accent: ["#DC143C", "#FFB6C1", "#FFD700"], // 强调色
    neutral: ["#F8F8FF", "#E6F3FF", "#654321"] // 中性色
  },
  lighting: "柔和自然光，左上45度角，避免强阴影",
  background: "简洁清爽，最多3-4元素，饱和度比主体低30%",
  composition: "主体居中偏左，三分法则，充足留白",
  texture: "轻微水彩晕染，柔和边缘，整体和谐"
};

/**
 * 环境场景规范
 */
export const ENVIRONMENT_STANDARDS = {
  forest: {
    trees: "翠绿色 (#228B22) 圆形树冠，棕色 (#8B4513) 树干",
    grass: "嫩绿色 (#90EE90) 草地，白黄粉小花点缀",
    path: "浅棕色 (#D2B48C) 土路，边缘自然不规则",
    sky: "淡蓝色 (#E6F3FF) 渐变，白色蓬松云朵",
    house: "棕色 (#8B4513) 木墙，红色 (#B22222) 屋顶",
    stream: "清澈蓝色 (#87CEEB)，光滑石头，绿色水草"
  }
};

/**
 * 生成LiblibAI专用的详细提示词 (支持多主题)
 * @param {string} sceneDescription - 场景描述
 * @param {Array} characters - 角色列表
 * @param {string} ageRange - 年龄范围
 * @param {string} theme - 绘本主题 (默认为友谊冒险)
 */
export function generateLiblibPrompt(sceneDescription, characters = [], ageRange = "6-8岁", theme = "friendship_adventure") {
  // 验证主题和角色
  const validation = validateThemeAndCharacters(theme, characters);
  if (!validation.valid) {
    console.warn('主题或角色验证失败:', validation.error);
    console.log('可用主题:', validation.availableThemes);
    console.log('可用角色:', validation.availableCharacters);
    // 回退到默认主题
    theme = "friendship_adventure";
    characters = characters.length > 0 ? characters : ["bobo", "lili"];
  }

  // 使用通用提示词生成器
  const universalPrompt = generateUniversalPrompt(sceneDescription, theme, characters, ageRange);

  // 为LiblibAI添加特定的技术要求
  const liblibSpecificPrompt = `
${universalPrompt}

LIBLIB AI SPECIFIC REQUIREMENTS:
- High-quality 1024x1024 resolution
- Optimized for controlnet and IPAdapter
- Clear subject-background separation
- Consistent lighting and shadows
- Smooth color transitions
- Child-friendly content only

CRITICAL CONSISTENCY RULES:
1. Character appearance must be PIXEL-PERFECT match to specifications
2. Use ONLY the specified hex color codes
3. Maintain EXACT same art style and lighting
4. Keep background elements to maximum 3-4 items
5. Ensure emotional expressions are clear and recognizable

Generate a wholesome, educational illustration that PERFECTLY matches the established visual consistency of this children's book series.`;

  console.log(`🎨 生成 "${STORYBOOK_THEMES[theme]?.name || theme}" 主题的LiblibAI提示词`);
  console.log(`👥 使用角色: ${characters.join(", ")}`);

  return liblibSpecificPrompt.trim();
}

/**
 * 向后兼容的提示词生成函数 (保持原有接口)
 * @deprecated 建议使用带主题参数的新版本
 */
export function generateLiblibPromptLegacy(sceneDescription, characters = [], ageRange = "6-8岁") {
  console.warn('⚠️ 使用了旧版generateLiblibPrompt，建议升级到支持主题的新版本');
  return generateLiblibPrompt(sceneDescription, characters, ageRange, "friendship_adventure");
}

/**
 * 生成Image2Image专用提示词（用于保持风格一致性）
 */
export function generateImage2ImagePrompt(sceneDescription, userAnswer, ageRange = "6-8岁") {
  return `
Create a children's book illustration that EXACTLY matches the reference image style:

SCENE UPDATE: ${sceneDescription}
USER INPUT: ${userAnswer}

STYLE MATCHING REQUIREMENTS (CRITICAL):
- EXACT same artistic technique as reference
- IDENTICAL color palette and saturation
- SAME line weight and style
- MATCHING lighting direction and intensity
- IDENTICAL character proportions and features
- SAME background complexity level

CHARACTER CONSISTENCY:
- Bear Bobo: Brown (#D2691E), red shirt (#DC143C), blue pants (#191970)
- Rabbit Lily: White (#F8F8FF), pink dress (#FFB6C1)
- Turtle Teacher: Green (#808000), brown shell (#8B4513), gold glasses (#FFD700)
- Squirrel Brothers: Brown (#A0522D), yellow/green vests (#FFD700/#32CD32)

CONTENT ADAPTATION:
- Incorporate user response: "${userAnswer}"
- Maintain happy, positive expressions
- Age-appropriate for ${ageRange}
- Family-friendly content only

CRITICAL: The new illustration must look like it was created by the SAME artist using the SAME techniques and materials as the reference image. Style consistency is more important than scene details.`;
}

/**
 * 验证生成图像的一致性（用于质量检查）
 */
export function validateImageConsistency(imageUrl, expectedCharacters = []) {
  // 这里可以实现图像分析逻辑
  // 目前返回基本验证结果
  return {
    isValid: true,
    score: 0.85,
    issues: [],
    suggestions: [
      "检查角色颜色是否符合规范",
      "验证背景复杂度是否适当",
      "确认表情是否清晰易懂"
    ]
  };
}
