// 为自闭症儿童语音交互绘本设计的提示词模板

// 故事生成提示词模板
const STORY_PROMPT_TEMPLATE = `
请为{age_range}岁的自闭症儿童创作一个以"{theme}"为主题的12页绘本故事。

要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的情感表达和社交互动场景
6. 在故事中随机安排3个交互环节（第4页、第8页和第11页），这些页面需要设计问题
7. 交互问题应鼓励自闭症儿童进行语言表达，不是简单的选择题
8. 每个交互问题都需要配备一个引导提示，用于在儿童30秒内没有回答时提供帮助

输出格式：
{
  "title": "故事标题",
  "pages": [
    {
      "page_number": 1,
      "content": "第1页内容...",
      "is_interactive": false
    },
    {
      "page_number": 4,
      "content": "第4页内容...",
      "is_interactive": true,
      "interactive_question": "问题内容...",
      "guidance_prompt": "引导提示内容..."
    },
    // 其他页面...
  ]
}
`;

// 针对不同主题的专门提示词模板
const THEME_SPECIFIC_PROMPTS = {
  "人际关系": `
请为{age_range}岁的自闭症儿童创作一个关于"人际关系"的12页绘本故事。

故事主题要求：
- 重点展现如何与他人建立友谊
- 包含分享、合作、互助的情节
- 展示如何处理与朋友的小冲突
- 教导基本的社交礼仪和沟通技巧
- 强调倾听和理解他人感受的重要性

角色设定建议：
- 主角：一个善良但有些内向的小动物
- 配角：不同性格的朋友们
- 情境：学校、游乐场、家庭聚会等社交场所

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的情感表达和社交互动场景
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题应鼓励自闭症儿童进行语言表达，不是简单的选择题
8. 每个交互问题都需要配备一个引导提示

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "人际关系",
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "imagePath": "/assets/images/page1.png"
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "imagePath": "/assets/images/page4.png"
    }
    // 其他页面...
  ]
}
`,

  "家庭生活": `
请为{age_range}岁的自闭症儿童创作一个关于"家庭生活"的12页绘本故事。

故事主题要求：
- 展现温馨的家庭日常生活
- 包含家庭成员之间的关爱和支持
- 教导家庭责任和基本的家务参与
- 展示如何表达对家人的爱和感谢
- 处理家庭中的小问题和情感交流

角色设定建议：
- 主角：一个可爱的小动物孩子
- 配角：爸爸、妈妈、兄弟姐妹、爷爷奶奶等
- 情境：家中的客厅、厨房、卧室、花园等

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的情感表达和家庭互动场景
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题应鼓励自闭症儿童进行语言表达，不是简单的选择题
8. 每个交互问题都需要配备一个引导提示

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "家庭生活",
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "imagePath": "/assets/images/page1.png"
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "imagePath": "/assets/images/page4.png"
    }
    // 其他页面...
  ]
}
`,

  "法律常识": `
请为{age_range}岁的自闭症儿童创作一个关于"法律常识"的12页绘本故事。

故事主题要求：
- 用简单易懂的方式介绍基本的法律概念
- 教导什么是对的行为，什么是错的行为
- 包含保护自己和他人的基本规则
- 展示遵守规则的重要性
- 教导如何在遇到问题时寻求帮助

角色设定建议：
- 主角：一个聪明好学的小动物
- 配角：警察叔叔、老师、家长等权威人物
- 情境：学校、公园、商店、马路等公共场所

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的规则教育和安全意识
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题应鼓励自闭症儿童进行语言表达，不是简单的选择题
8. 每个交互问题都需要配备一个引导提示

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "法律常识",
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "imagePath": "/assets/images/page1.png"
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "imagePath": "/assets/images/page4.png"
    }
    // 其他页面...
  ]
}
`,

  "人伦道德": `
请为{age_range}岁的自闭症儿童创作一个关于"人伦道德"的12页绘本故事。

故事主题要求：
- 教导基本的道德品质：诚实、善良、勇敢、感恩
- 展现如何尊重长辈和关爱他人
- 包含助人为乐和无私奉献的情节
- 教导如何做出正确的道德选择
- 强调同情心和责任感的培养

角色设定建议：
- 主角：一个有爱心的小动物
- 配角：需要帮助的朋友、长辈、陌生人等
- 情境：社区、学校、家庭等各种需要道德选择的场景

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的道德教育和品格培养
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题应鼓励自闭症儿童进行语言表达，不是简单的选择题
8. 每个交互问题都需要配备一个引导提示

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "人伦道德",
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "imagePath": "/assets/images/page1.png"
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "imagePath": "/assets/images/page4.png"
    }
    // 其他页面...
  ]
}
`
};

// 增强的角色一致性描述
const ENHANCED_CHARACTER_DESCRIPTIONS = {
  bobo: `小熊波波：
- 外观：棕色毛发的小熊，毛色为温暖的焦糖棕色(#D2691E)
- 脸部：圆润的脸蛋，直径约为身高的1/4，黑色的小圆鼻子，位置居中
- 眼睛：大而圆的黑色眼睛，眼白清晰，眼神友善温和，眼睛间距为一个眼睛的宽度
- 耳朵：小而圆的耳朵，位于头顶两侧，内侧为浅棕色
- 身材：略微圆润的体型，身高约为画面高度的1/3
- 服装：鲜红色(#DC143C)的圆领短袖上衣，深蓝色(#191970)的短裤，无其他装饰
- 姿态：站立时略微前倾，表现友好和好奇`,

  lili: `小兔莉莉：
- 外观：灰白色的兔子，主体为珍珠白(#F8F8FF)，耳朵尖端为浅灰色(#D3D3D3)
- 脸部：椭圆形脸蛋，比波波略小，粉红色的三角形鼻子
- 眼睛：温柔的黑色眼睛，略小于波波，眼神柔和
- 耳朵：长耳朵，长度约为身高的1/3，通常一只竖立一只略微下垂
- 身材：纤细优雅，身高略小于波波
- 服装：粉红色(#FFB6C1)的连衣裙，有时佩戴小花朵发饰(黄色小花)
- 姿态：优雅端庄，动作轻柔`,

  teacher: `乌龟老师：
- 外观：绿色的乌龟，壳为深棕色(#8B4513)，头部和四肢为橄榄绿(#808000)
- 脸部：和蔼的表情，略微方形的脸
- 眼睛：戴着圆形金边眼镜，眼神智慧而慈祥
- 特征：壳上有清晰的六边形花纹，脖子略长
- 身材：稳重的体型，通常坐着或站立时很稳定
- 配饰：金色圆框眼镜，有时手持书本或教具
- 姿态：沉稳可靠，动作缓慢而有条理`,

  squirrels: `松鼠兄弟：
- 外观：红棕色(#A0522D)的松鼠，毛发蓬松
- 脸部：尖尖的小脸，黑色的小鼻子
- 眼睛：明亮活泼的黑色眼睛，眼神机灵
- 尾巴：大而蓬松的尾巴，占身长的2/3
- 身材：小巧灵活，身高约为波波的2/3
- 服装：哥哥穿黄色(#FFD700)背心，弟弟穿绿色(#32CD32)背心
- 姿态：活泼好动，经常做跳跃或攀爬动作`
};

// 详细的风格一致性指南
const DETAILED_STYLE_GUIDE = `
艺术风格规范：
1. 绘画技法：水彩画风格，带有轻微的纸张纹理效果
2. 线条：清晰的黑色轮廓线，线宽一致(2-3px)，圆润的线条连接
3. 色彩方案：
   - 主色调：温暖的棕色系(#D2691E, #CD853F, #DEB887)
   - 辅助色：柔和的绿色(#90EE90, #98FB98)、蓝色(#87CEEB, #B0E0E6)
   - 强调色：温和的红色(#DC143C)、粉色(#FFB6C1)、黄色(#FFD700)
4. 光影：柔和的自然光，避免强烈阴影，使用浅色调表现高光
5. 背景：简洁清爽，元素不超过3-4个，颜色饱和度低于主体
6. 构图：主体居中或偏左，遵循三分法则，留白充足
7. 纹理：轻微的水彩晕染效果，边缘柔和不锐利
8. 比例：角色比例协调，环境元素比例真实但略微卡通化`;

// 场景环境一致性描述
const ENVIRONMENT_CONSISTENCY = `
森林环境标准：
- 树木：绿色(#228B22)的圆形树冠，棕色(#8B4513)的树干，高度变化自然
- 草地：嫩绿色(#90EE90)的草地，有小花点缀(白色、黄色、粉色小花)
- 小路：浅棕色(#D2B48C)的土路，边缘不规则但清晰
- 天空：淡蓝色(#E6F3FF)渐变，可有白色云朵点缀
- 房屋：木质结构，棕色(#8B4513)墙面，红色(#B22222)屋顶
- 小溪：清澈的蓝色(#87CEEB)，有小石头和水草装饰`;

// 图片生成提示词模板（增强版）
const IMAGE_PROMPT_TEMPLATE = `
为自闭症儿童绘本创作一张插图，描述以下场景："{scene_description}"

角色外观要求：
{character_descriptions}

风格一致性要求：
{style_guide}

环境设定：
{environment_guide}

技术要求：
1. 适合{age_range}岁自闭症儿童的视觉感知特点
2. 避免过于复杂或混乱的背景元素
3. 角色表情要明确、易于识别和理解
4. 图像应具有温暖、友好、安全的氛围
5. 避免使用文字或抽象符号
6. 确保与绘本其他页面的视觉连贯性

重要：请严格遵循上述角色外观和风格描述，确保生成的插图与整个绘本系列保持完全一致的视觉风格。
`;

// 专为LiblibAI image2image功能设计的提示词模板（优化版）
const IMAGE2IMAGE_PROMPT_TEMPLATE = `
Create a cheerful children's storybook illustration using the reference image style:

Scene: {scene_description}

Style Match:
- Same artistic style as reference
- Same watercolor technique
- Same color palette
- Same line style
- Same character design
- Same lighting style

Content Update:
- Update scene based on: {user_answer}
- Add new elements in same style
- Show happy character expressions
- Age-appropriate for {age_range} years old
- Family-friendly content

Create a wholesome children's book illustration that matches the reference style perfectly.
`;

// 交互式插画生成提示词模板（优化版，避免敏感内容检测）
const INTERACTIVE_ILLUSTRATION_PROMPT_TEMPLATE = `
Create a cheerful children's book illustration based on this story response:

Story Response: "{user_answer}"
Context: "{story_context}"
Page: {page_number}

Main Characters:
- Bear Bobo: Brown bear, round face, happy expression, red shirt, blue pants
- Rabbit Lily: Gray rabbit, long ears, kind expression, pink dress
- Turtle Teacher: Green turtle, brown shell, glasses, friendly look
- Squirrel Friends: Brown squirrels, fluffy tails, colorful clothes

Art Style:
1. Bright cheerful children's book style
2. Soft pastel colors
3. Simple clean shapes
4. Smooth watercolor look
5. Clean simple background
6. Happy character expressions
7. Welcoming scene design
8. Elements from the story response

Create a wholesome family-friendly illustration that matches children's storybook art style.
`;

// 交互问题生成提示词模板
const INTERACTIVE_QUESTION_PROMPT_TEMPLATE = `
为{age_range}岁自闭症儿童设计一个关于"{context}"的交互问题。

故事背景：
"{story_context}"

要求：
1. 问题应促进语言表达，而非简单的是/否回答
2. 问题应与故事情节和主题"{theme}"相关
3. 问题应考虑自闭症儿童的认知特点
4. 问题应鼓励分享个人经历或想法
5. 问题应有明确的焦点，避免模糊或抽象
6. 同时设计一个引导提示，用于在儿童30秒内没有回答时提供帮助

输出格式：
{
  "question": "问题内容...",
  "guidance_prompt": "引导提示内容..."
}
`;

// 性能评估提示词模板
const PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE = `
分析自闭症儿童在以下交互问题中的回答表现：

问题1："{question1}"
回答1："{answer1}"

问题2："{question2}"
回答2："{answer2}"

问题3："{question3}"
回答3："{answer3}"

请从以下四个维度进行评估（满分5分）：
1. 语言词汇量：评估词汇丰富度、表达多样性
2. 思维逻辑：评估因果关系理解、逻辑推理能力
3. 社会适应：评估社交规则理解、人际互动意识
4. 情感识别：评估情感表达、共情能力

对于每个维度，请提供具体分析和改进建议。

输出格式：
{
  "scores": {
    "language_vocabulary": 分数,
    "logical_thinking": 分数,
    "social_adaptation": 分数,
    "emotional_recognition": 分数
  },
  "analysis": {
    "language_vocabulary": "分析和建议...",
    "logical_thinking": "分析和建议...",
    "social_adaptation": "分析和建议...",
    "emotional_recognition": "分析和建议..."
  },
  "overall_recommendation": "综合建议..."
}
`;

export {
  STORY_PROMPT_TEMPLATE,
  THEME_SPECIFIC_PROMPTS,
  IMAGE_PROMPT_TEMPLATE,
  IMAGE2IMAGE_PROMPT_TEMPLATE,
  INTERACTIVE_ILLUSTRATION_PROMPT_TEMPLATE,
  INTERACTIVE_QUESTION_PROMPT_TEMPLATE,
  PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE
};
