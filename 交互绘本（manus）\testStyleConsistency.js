// 风格一致性系统测试脚本
// 用于验证新的风格一致性功能是否正常工作

import { generateLiblibPrompt, generateImage2ImagePrompt } from './src/services/styleConsistencyManager.js';
import { validateImageStyle } from './src/services/styleConsistencyValidator.js';

/**
 * 测试风格一致性管理器
 */
async function testStyleConsistencyManager() {
  console.log('🧪 开始测试风格一致性管理器...\n');

  // 测试1: 基础提示词生成
  console.log('📝 测试1: 基础提示词生成');
  const basicPrompt = generateLiblibPrompt(
    '小熊波波和小兔莉莉在森林中快乐地玩耍',
    ['bobo', 'lili'],
    '6-8岁'
  );
  console.log('✅ 基础提示词生成成功');
  console.log('📄 提示词长度:', basicPrompt.length, '字符');
  console.log('🔍 包含角色规范:', basicPrompt.includes('Bear Bobo') && basicPrompt.includes('Rabbit Lily'));
  console.log('🎨 包含颜色代码:', basicPrompt.includes('#D2691E') && basicPrompt.includes('#FFB6C1'));
  console.log('');

  // 测试2: Image2Image提示词生成
  console.log('📝 测试2: Image2Image提示词生成');
  const image2imagePrompt = generateImage2ImagePrompt(
    '波波在帮助莉莉摘苹果',
    '我想帮助朋友',
    '6-8岁'
  );
  console.log('✅ Image2Image提示词生成成功');
  console.log('📄 提示词长度:', image2imagePrompt.length, '字符');
  console.log('🔍 包含用户回答:', image2imagePrompt.includes('我想帮助朋友'));
  console.log('🎭 包含风格匹配要求:', image2imagePrompt.includes('EXACT same artistic technique'));
  console.log('');

  // 测试3: 不同角色组合
  console.log('📝 测试3: 不同角色组合');
  const teacherPrompt = generateLiblibPrompt(
    '乌龟老师在给松鼠兄弟上课',
    ['teacher', 'squirrels'],
    '6-8岁'
  );
  console.log('✅ 教师场景提示词生成成功');
  console.log('🐢 包含乌龟老师:', teacherPrompt.includes('Turtle Teacher'));
  console.log('🐿️ 包含松鼠兄弟:', teacherPrompt.includes('Squirrel Brothers'));
  console.log('');

  return {
    basicPrompt,
    image2imagePrompt,
    teacherPrompt
  };
}

/**
 * 测试风格一致性验证器
 */
async function testStyleConsistencyValidator() {
  console.log('🔍 开始测试风格一致性验证器...\n');

  // 模拟图像URL
  const testImageUrl = 'https://example.com/test-image.jpg';
  const expectedCharacters = ['bobo', 'lili'];

  console.log('📝 测试图像一致性验证');
  console.log('🖼️ 测试图像URL:', testImageUrl);
  console.log('👥 期望角色:', expectedCharacters);

  try {
    const validation = await validateImageStyle(testImageUrl, expectedCharacters);
    
    console.log('✅ 验证完成');
    console.log('📊 总体评分:', (validation.overall_score * 100).toFixed(1) + '%');
    console.log('✔️ 是否一致:', validation.is_consistent ? '是' : '否');
    console.log('📋 详细评分:');
    
    Object.entries(validation.detailed_scores).forEach(([key, score]) => {
      console.log(`   - ${key}: ${(score * 100).toFixed(1)}%`);
    });

    if (validation.issues.length > 0) {
      console.log('⚠️ 发现问题:');
      validation.issues.forEach(issue => console.log(`   - ${issue}`));
    }

    if (validation.suggestions.length > 0) {
      console.log('💡 改进建议:');
      validation.suggestions.forEach(suggestion => console.log(`   - ${suggestion}`));
    }

    console.log('');
    return validation;
  } catch (error) {
    console.error('❌ 验证过程出错:', error.message);
    return null;
  }
}

/**
 * 测试完整工作流程
 */
async function testCompleteWorkflow() {
  console.log('🔄 开始测试完整工作流程...\n');

  // 模拟用户回答
  const userAnswer = '我想和朋友一起在花园里玩耍，我们可以一起摘花和追蝴蝶';
  const context = {
    currentPage: {
      content: '波波和莉莉来到了美丽的花园',
      isInteractive: true,
      question: '你想和朋友一起做什么有趣的事情？'
    }
  };

  console.log('📝 用户回答:', userAnswer);
  console.log('📖 故事上下文:', context.currentPage.content);
  console.log('❓ 交互问题:', context.currentPage.question);
  console.log('');

  // 1. 生成场景描述
  const sceneDescription = `${userAnswer}。故事情境：${context.currentPage.content}。交互问题：${context.currentPage.question}`;
  
  // 2. 生成提示词
  const prompt = generateLiblibPrompt(sceneDescription, ['bobo', 'lili'], '6-8岁');
  
  console.log('✅ 提示词生成完成');
  console.log('📏 提示词长度:', prompt.length, '字符');
  console.log('');

  // 3. 模拟图像生成（实际应用中会调用LiblibAI）
  console.log('🎨 模拟图像生成...');
  const mockImageUrl = `https://mock-liblib.com/generated-image-${Date.now()}.jpg`;
  console.log('🖼️ 模拟生成的图像URL:', mockImageUrl);
  console.log('');

  // 4. 验证图像一致性
  const validation = await validateImageStyle(mockImageUrl, ['bobo', 'lili']);
  
  console.log('🔍 图像一致性验证结果:');
  console.log('📊 总体评分:', (validation.overall_score * 100).toFixed(1) + '%');
  console.log('✔️ 是否符合标准:', validation.is_consistent ? '是' : '否');
  console.log('');

  return {
    userAnswer,
    sceneDescription,
    prompt,
    mockImageUrl,
    validation
  };
}

/**
 * 性能测试
 */
async function performanceTest() {
  console.log('⚡ 开始性能测试...\n');

  const testCases = [
    { scene: '波波在读书', characters: ['bobo'] },
    { scene: '莉莉在画画', characters: ['lili'] },
    { scene: '老师在教学', characters: ['teacher'] },
    { scene: '松鼠在玩耍', characters: ['squirrels'] },
    { scene: '大家一起野餐', characters: ['bobo', 'lili', 'teacher', 'squirrels'] }
  ];

  const startTime = Date.now();
  const results = [];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    const caseStartTime = Date.now();
    
    // 生成提示词
    const prompt = generateLiblibPrompt(testCase.scene, testCase.characters, '6-8岁');
    
    // 模拟验证
    const mockUrl = `https://test.com/image-${i}.jpg`;
    const validation = await validateImageStyle(mockUrl, testCase.characters);
    
    const caseEndTime = Date.now();
    const caseDuration = caseEndTime - caseStartTime;
    
    results.push({
      case: i + 1,
      scene: testCase.scene,
      characters: testCase.characters,
      promptLength: prompt.length,
      validationScore: validation.overall_score,
      duration: caseDuration
    });
    
    console.log(`✅ 测试用例 ${i + 1}/5 完成 (${caseDuration}ms)`);
  }

  const totalTime = Date.now() - startTime;
  const avgTime = totalTime / testCases.length;
  const avgScore = results.reduce((sum, r) => sum + r.validationScore, 0) / results.length;

  console.log('\n📊 性能测试结果:');
  console.log(`⏱️ 总耗时: ${totalTime}ms`);
  console.log(`⚡ 平均耗时: ${avgTime.toFixed(1)}ms/案例`);
  console.log(`📈 平均评分: ${(avgScore * 100).toFixed(1)}%`);
  console.log('');

  return results;
}

/**
 * 主测试函数
 */
async function runAllTests() {
  console.log('🚀 开始风格一致性系统全面测试\n');
  console.log('=' * 60);
  console.log('');

  try {
    // 1. 测试风格一致性管理器
    const managerResults = await testStyleConsistencyManager();
    
    // 2. 测试风格一致性验证器
    const validatorResults = await testStyleConsistencyValidator();
    
    // 3. 测试完整工作流程
    const workflowResults = await testCompleteWorkflow();
    
    // 4. 性能测试
    const performanceResults = await performanceTest();
    
    console.log('🎉 所有测试完成！');
    console.log('');
    console.log('📋 测试总结:');
    console.log('✅ 风格一致性管理器: 正常');
    console.log('✅ 风格一致性验证器: 正常');
    console.log('✅ 完整工作流程: 正常');
    console.log('✅ 性能测试: 正常');
    console.log('');
    console.log('🎯 系统已准备就绪，可以开始使用！');
    
    return {
      manager: managerResults,
      validator: validatorResults,
      workflow: workflowResults,
      performance: performanceResults
    };
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    console.error('📍 错误堆栈:', error.stack);
    return null;
  }
}

// 如果直接运行此脚本，执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().then(results => {
    if (results) {
      console.log('\n✨ 测试成功完成！');
      process.exit(0);
    } else {
      console.log('\n💥 测试失败！');
      process.exit(1);
    }
  });
}

export { runAllTests, testStyleConsistencyManager, testStyleConsistencyValidator };
