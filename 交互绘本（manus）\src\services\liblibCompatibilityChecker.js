// LiblibAI兼容性检查器
// 确保生成的提示词符合LiblibAI的要求

/**
 * LiblibAI提示词规范
 */
export const LIBLIB_PROMPT_SPECS = {
  maxLength: 500,           // 推荐最大长度
  absoluteMaxLength: 1000,  // 绝对最大长度
  minLength: 10,            // 最小长度
  
  // 禁用的字符和内容
  forbiddenChars: ['{', '}', '[', ']', '\\', '|'],
  forbiddenWords: ['NSFW', 'adult', 'violence', 'blood'],
  
  // 推荐的格式
  recommendedFormat: {
    startWithSubject: true,    // 以主体开始
    useSimpleLanguage: true,   // 使用简单语言
    avoidComplexStructure: true, // 避免复杂结构
    preferEnglish: true        // 优先使用英文
  }
};

/**
 * 检查提示词是否符合LiblibAI规范
 */
export function checkLiblibCompatibility(prompt) {
  const issues = [];
  const warnings = [];
  const suggestions = [];

  // 1. 长度检查
  if (prompt.length < LIBLIB_PROMPT_SPECS.minLength) {
    issues.push(`提示词过短 (${prompt.length} 字符)，最少需要 ${LIBLIB_PROMPT_SPECS.minLength} 字符`);
  } else if (prompt.length > LIBLIB_PROMPT_SPECS.absoluteMaxLength) {
    issues.push(`提示词过长 (${prompt.length} 字符)，超过绝对限制 ${LIBLIB_PROMPT_SPECS.absoluteMaxLength} 字符`);
  } else if (prompt.length > LIBLIB_PROMPT_SPECS.maxLength) {
    warnings.push(`提示词较长 (${prompt.length} 字符)，建议控制在 ${LIBLIB_PROMPT_SPECS.maxLength} 字符以内`);
  }

  // 2. 禁用字符检查
  const foundForbiddenChars = LIBLIB_PROMPT_SPECS.forbiddenChars.filter(char => 
    prompt.includes(char)
  );
  if (foundForbiddenChars.length > 0) {
    issues.push(`包含禁用字符: ${foundForbiddenChars.join(', ')}`);
  }

  // 3. 禁用词汇检查
  const foundForbiddenWords = LIBLIB_PROMPT_SPECS.forbiddenWords.filter(word => 
    prompt.toLowerCase().includes(word.toLowerCase())
  );
  if (foundForbiddenWords.length > 0) {
    issues.push(`包含禁用词汇: ${foundForbiddenWords.join(', ')}`);
  }

  // 4. 格式检查
  if (prompt.includes('\n\n')) {
    warnings.push('包含多行结构，建议简化为单行格式');
  }

  if (prompt.includes('CRITICAL:') || prompt.includes('REQUIREMENTS:')) {
    warnings.push('包含结构化标记，建议使用自然语言描述');
  }

  // 5. 语言检查
  const chineseCharCount = (prompt.match(/[\u4e00-\u9fa5]/g) || []).length;
  const totalCharCount = prompt.length;
  const chineseRatio = chineseCharCount / totalCharCount;

  if (chineseRatio > 0.5) {
    suggestions.push('中文比例较高，建议增加英文描述以提高兼容性');
  }

  // 6. 复杂度检查
  const commaCount = (prompt.match(/,/g) || []).length;
  const periodCount = (prompt.match(/\./g) || []).length;
  const complexityScore = commaCount + periodCount * 2;

  if (complexityScore > 20) {
    warnings.push('句子结构较复杂，建议简化表达');
  }

  // 7. 生成改进建议
  if (prompt.length > LIBLIB_PROMPT_SPECS.maxLength) {
    suggestions.push('考虑移除详细的技术要求，保留核心描述');
  }

  if (foundForbiddenChars.length > 0) {
    suggestions.push('移除特殊字符，使用自然语言描述');
  }

  if (chineseRatio > 0.7) {
    suggestions.push('增加英文关键词，如 "children\'s book illustration", "watercolor style"');
  }

  // 计算兼容性评分
  let score = 100;
  score -= issues.length * 20;      // 严重问题扣20分
  score -= warnings.length * 10;    // 警告扣10分
  score -= suggestions.length * 5;  // 建议扣5分
  score = Math.max(0, score);

  return {
    isCompatible: issues.length === 0,
    score: score,
    issues: issues,
    warnings: warnings,
    suggestions: suggestions,
    stats: {
      length: prompt.length,
      chineseRatio: Math.round(chineseRatio * 100),
      complexityScore: complexityScore
    }
  };
}

/**
 * 自动修复提示词以提高兼容性
 */
export function fixLiblibCompatibility(prompt) {
  let fixedPrompt = prompt;
  const fixes = [];

  // 1. 移除禁用字符
  LIBLIB_PROMPT_SPECS.forbiddenChars.forEach(char => {
    if (fixedPrompt.includes(char)) {
      fixedPrompt = fixedPrompt.replace(new RegExp('\\' + char, 'g'), '');
      fixes.push(`移除禁用字符: ${char}`);
    }
  });

  // 2. 简化多行结构
  if (fixedPrompt.includes('\n\n')) {
    fixedPrompt = fixedPrompt.replace(/\n\n+/g, '. ');
    fixes.push('简化多行结构为单行');
  }

  // 3. 移除结构化标记
  const structureMarkers = [
    /CRITICAL[^:]*:/gi,
    /REQUIREMENTS[^:]*:/gi,
    /SPECIFICATIONS[^:]*:/gi,
    /TECHNICAL[^:]*:/gi,
    /LIBLIB[^:]*:/gi
  ];

  structureMarkers.forEach(marker => {
    if (marker.test(fixedPrompt)) {
      fixedPrompt = fixedPrompt.replace(marker, '');
      fixes.push('移除结构化标记');
    }
  });

  // 4. 简化列表结构
  fixedPrompt = fixedPrompt.replace(/^\s*[-\d\.]\s*/gm, '');
  if (fixes.length === 0 && fixedPrompt !== prompt) {
    fixes.push('简化列表结构');
  }

  // 5. 长度控制
  if (fixedPrompt.length > LIBLIB_PROMPT_SPECS.maxLength) {
    // 保留前面的核心内容
    const sentences = fixedPrompt.split(/[.!?]+/);
    let truncatedPrompt = '';
    
    for (const sentence of sentences) {
      if ((truncatedPrompt + sentence).length <= LIBLIB_PROMPT_SPECS.maxLength - 10) {
        truncatedPrompt += sentence + '. ';
      } else {
        break;
      }
    }
    
    if (truncatedPrompt.length > 0) {
      fixedPrompt = truncatedPrompt.trim();
      fixes.push(`截断长度从 ${prompt.length} 到 ${fixedPrompt.length} 字符`);
    }
  }

  // 6. 清理多余空格
  fixedPrompt = fixedPrompt.replace(/\s+/g, ' ').trim();

  return {
    originalPrompt: prompt,
    fixedPrompt: fixedPrompt,
    fixes: fixes,
    improvement: {
      lengthReduction: prompt.length - fixedPrompt.length,
      compatibilityBefore: checkLiblibCompatibility(prompt).score,
      compatibilityAfter: checkLiblibCompatibility(fixedPrompt).score
    }
  };
}

/**
 * 生成LiblibAI优化的提示词
 */
export function generateOptimizedPrompt(sceneDescription, characters = [], style = "children's book illustration") {
  // 构建简洁的角色描述
  const characterDesc = characters.length > 0 ? 
    `featuring ${characters.join(' and ')}` : 
    'featuring cute characters';

  // 构建优化的提示词
  const optimizedPrompt = `${sceneDescription}, ${style}, ${characterDesc}, warm and friendly atmosphere, simple background, wholesome and safe, age-appropriate for children`;

  // 检查并修复兼容性
  const fixResult = fixLiblibCompatibility(optimizedPrompt);
  
  return {
    prompt: fixResult.fixedPrompt,
    compatibility: checkLiblibCompatibility(fixResult.fixedPrompt),
    optimizations: fixResult.fixes
  };
}

/**
 * 批量检查提示词兼容性
 */
export function batchCheckCompatibility(prompts) {
  const results = prompts.map((prompt, index) => ({
    index: index,
    prompt: prompt.substring(0, 50) + '...',
    compatibility: checkLiblibCompatibility(prompt)
  }));

  const summary = {
    total: prompts.length,
    compatible: results.filter(r => r.compatibility.isCompatible).length,
    averageScore: results.reduce((sum, r) => sum + r.compatibility.score, 0) / prompts.length,
    commonIssues: {}
  };

  // 统计常见问题
  results.forEach(result => {
    result.compatibility.issues.forEach(issue => {
      summary.commonIssues[issue] = (summary.commonIssues[issue] || 0) + 1;
    });
  });

  return {
    results: results,
    summary: summary
  };
}

/**
 * 提示词质量评估
 */
export function assessPromptQuality(prompt) {
  const compatibility = checkLiblibCompatibility(prompt);
  
  // 内容质量评估
  const hasSubject = /^[^,]+/.test(prompt.trim());
  const hasStyle = /illustration|style|art|drawing|painting/.test(prompt.toLowerCase());
  const hasAtmosphere = /warm|friendly|happy|peaceful|cozy/.test(prompt.toLowerCase());
  const hasAgeAppropriate = /children|kid|child|age-appropriate/.test(prompt.toLowerCase());
  
  const contentScore = [hasSubject, hasStyle, hasAtmosphere, hasAgeAppropriate]
    .filter(Boolean).length * 25;

  const overallScore = (compatibility.score + contentScore) / 2;

  return {
    overallScore: Math.round(overallScore),
    compatibility: compatibility,
    content: {
      hasSubject,
      hasStyle,
      hasAtmosphere,
      hasAgeAppropriate,
      score: contentScore
    },
    recommendation: overallScore >= 80 ? 'excellent' : 
                   overallScore >= 60 ? 'good' : 
                   overallScore >= 40 ? 'fair' : 'poor'
  };
}

// 便捷函数
export function isLiblibCompatible(prompt) {
  return checkLiblibCompatibility(prompt).isCompatible;
}

export function getCompatibilityScore(prompt) {
  return checkLiblibCompatibility(prompt).score;
}

export function quickFix(prompt) {
  return fixLiblibCompatibility(prompt).fixedPrompt;
}
