// 主题选择组件
// 允许用户选择不同的绘本主题

import React, { useState, useEffect } from 'react';
import { 
  getAvailableThemes, 
  getCurrentTheme, 
  setTheme, 
  getThemeCharacters,
  getRecommendedCharacters 
} from '../services/themeManager.js';

interface Theme {
  id: string;
  name: string;
  description: string;
  mainThemes: string[];
  targetAge: string;
  artStyle: string;
  characterCount: number;
  environmentCount: number;
  isCustom?: boolean;
}

interface Character {
  id: string;
  name: string;
  role: string;
  personality: string;
  appearance: any;
}

interface CharacterCombination {
  name: string;
  characters: string[];
  description: string;
}

export const ThemeSelector: React.FC = () => {
  const [availableThemes, setAvailableThemes] = useState<Theme[]>([]);
  const [currentTheme, setCurrentTheme] = useState<string>('');
  const [selectedTheme, setSelectedTheme] = useState<string>('');
  const [themeCharacters, setThemeCharacters] = useState<Character[]>([]);
  const [recommendedCombinations, setRecommendedCombinations] = useState<CharacterCombination[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // 初始化数据
  useEffect(() => {
    loadThemeData();
  }, []);

  // 当选择的主题改变时，更新角色信息
  useEffect(() => {
    if (selectedTheme) {
      loadThemeDetails(selectedTheme);
    }
  }, [selectedTheme]);

  const loadThemeData = async () => {
    try {
      const themes = getAvailableThemes();
      const current = getCurrentTheme();
      
      setAvailableThemes(themes);
      setCurrentTheme(current);
      setSelectedTheme(current);
      
      console.log('📚 加载主题数据:', themes.length, '个主题');
    } catch (error) {
      console.error('❌ 加载主题数据失败:', error);
    }
  };

  const loadThemeDetails = async (themeId: string) => {
    try {
      const characters = getThemeCharacters(themeId);
      const combinations = getRecommendedCharacters(themeId);
      
      setThemeCharacters(characters);
      setRecommendedCombinations(combinations);
      
      console.log(`🎭 加载主题 "${themeId}" 详情:`, characters.length, '个角色');
    } catch (error) {
      console.error('❌ 加载主题详情失败:', error);
    }
  };

  const handleThemeChange = async (themeId: string) => {
    if (themeId === currentTheme) return;
    
    setIsLoading(true);
    try {
      const success = setTheme(themeId);
      if (success) {
        setCurrentTheme(themeId);
        setSelectedTheme(themeId);
        console.log(`✅ 主题切换成功: ${themeId}`);
        
        // 触发主题变更事件
        window.dispatchEvent(new CustomEvent('themeChanged', { 
          detail: { themeId, themeName: availableThemes.find(t => t.id === themeId)?.name }
        }));
      } else {
        console.error('❌ 主题切换失败');
      }
    } catch (error) {
      console.error('❌ 主题切换出错:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getThemeIcon = (themeId: string) => {
    const icons: { [key: string]: string } = {
      friendship_adventure: '🐻',
      family_life: '🏠',
      legal_awareness: '⚖️',
      moral_character: '⭐'
    };
    return icons[themeId] || '📚';
  };

  const getAgeRangeColor = (age: string) => {
    if (age.includes('5-7')) return 'bg-green-100 text-green-800';
    if (age.includes('6-8')) return 'bg-blue-100 text-blue-800';
    if (age.includes('7-9')) return 'bg-purple-100 text-purple-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="theme-selector bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          🎨 绘本主题选择
        </h2>
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          {showDetails ? '隐藏详情' : '显示详情'}
        </button>
      </div>

      {/* 当前主题显示 */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
        <div className="flex items-center">
          <span className="text-2xl mr-3">
            {getThemeIcon(currentTheme)}
          </span>
          <div>
            <h3 className="font-semibold text-blue-800">
              当前主题: {availableThemes.find(t => t.id === currentTheme)?.name}
            </h3>
            <p className="text-blue-600 text-sm">
              {availableThemes.find(t => t.id === currentTheme)?.description}
            </p>
          </div>
        </div>
      </div>

      {/* 主题选择网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {availableThemes.map((theme) => (
          <div
            key={theme.id}
            className={`
              p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
              ${selectedTheme === theme.id 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }
              ${currentTheme === theme.id ? 'ring-2 ring-green-500' : ''}
            `}
            onClick={() => setSelectedTheme(theme.id)}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-center">
                <span className="text-2xl mr-3">
                  {getThemeIcon(theme.id)}
                </span>
                <div>
                  <h3 className="font-semibold text-gray-800">
                    {theme.name}
                    {theme.isCustom && (
                      <span className="ml-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">
                        自定义
                      </span>
                    )}
                  </h3>
                  <p className="text-gray-600 text-sm mt-1">
                    {theme.description}
                  </p>
                </div>
              </div>
              {currentTheme === theme.id && (
                <span className="text-green-500 font-bold">✓</span>
              )}
            </div>

            <div className="mt-3 flex flex-wrap gap-2">
              <span className={`px-2 py-1 text-xs rounded ${getAgeRangeColor(theme.targetAge)}`}>
                {theme.targetAge}
              </span>
              <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                {theme.characterCount} 角色
              </span>
              <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                {theme.artStyle}
              </span>
            </div>

            <div className="mt-2 flex flex-wrap gap-1">
              {theme.mainThemes.map((mainTheme, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded"
                >
                  {mainTheme}
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* 主题切换按钮 */}
      {selectedTheme !== currentTheme && (
        <div className="mb-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-semibold text-yellow-800">
                切换到: {availableThemes.find(t => t.id === selectedTheme)?.name}
              </h4>
              <p className="text-yellow-700 text-sm">
                这将改变所有新生成插画的风格和角色
              </p>
            </div>
            <button
              onClick={() => handleThemeChange(selectedTheme)}
              disabled={isLoading}
              className={`
                px-6 py-2 rounded-md font-medium transition-colors
                ${isLoading 
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                  : 'bg-yellow-500 text-white hover:bg-yellow-600'
                }
              `}
            >
              {isLoading ? '切换中...' : '确认切换'}
            </button>
          </div>
        </div>
      )}

      {/* 主题详情 */}
      {showDetails && selectedTheme && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-gray-800 mb-4">
            📋 主题详情: {availableThemes.find(t => t.id === selectedTheme)?.name}
          </h4>

          {/* 角色列表 */}
          <div className="mb-4">
            <h5 className="font-medium text-gray-700 mb-2">👥 角色列表:</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {themeCharacters.map((character) => (
                <div key={character.id} className="p-2 bg-white rounded border">
                  <div className="font-medium text-sm">{character.name}</div>
                  <div className="text-xs text-gray-600">{character.role} - {character.personality}</div>
                </div>
              ))}
            </div>
          </div>

          {/* 推荐角色组合 */}
          <div>
            <h5 className="font-medium text-gray-700 mb-2">🎭 推荐角色组合:</h5>
            <div className="space-y-2">
              {recommendedCombinations.map((combo, index) => (
                <div key={index} className="p-2 bg-white rounded border">
                  <div className="font-medium text-sm">{combo.name}</div>
                  <div className="text-xs text-gray-600 mb-1">{combo.description}</div>
                  <div className="text-xs text-blue-600">
                    角色: {combo.characters.join(', ')}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThemeSelector;
