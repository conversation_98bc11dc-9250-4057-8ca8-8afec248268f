// 风格一致性验证器
// 用于检查生成的插画是否符合风格规范

import { CHARACTER_SPECIFICATIONS, ARTISTIC_STYLE_GUIDE } from './styleConsistencyManager.js';

/**
 * 图像一致性检查器
 */
export class StyleConsistencyValidator {
  constructor() {
    this.validationRules = {
      colorConsistency: 0.3,    // 颜色一致性权重
      characterAccuracy: 0.4,   // 角色准确性权重
      styleMatching: 0.2,       // 风格匹配权重
      backgroundSimplicity: 0.1 // 背景简洁性权重
    };
  }

  /**
   * 验证生成图像的风格一致性
   * @param {string} imageUrl - 生成的图像URL
   * @param {Array} expectedCharacters - 期望的角色列表
   * @param {Object} referenceImage - 参考图像信息
   * @returns {Object} 验证结果
   */
  async validateImageConsistency(imageUrl, expectedCharacters = [], referenceImage = null) {
    console.log('🔍 开始风格一致性验证...');
    console.log('📸 图像URL:', imageUrl);
    console.log('👥 期望角色:', expectedCharacters);

    const validationResult = {
      overall_score: 0,
      is_consistent: false,
      detailed_scores: {},
      issues: [],
      suggestions: [],
      timestamp: new Date().toISOString()
    };

    try {
      // 1. 颜色一致性检查
      const colorScore = await this.validateColorConsistency(imageUrl);
      validationResult.detailed_scores.color_consistency = colorScore;

      // 2. 角色准确性检查
      const characterScore = await this.validateCharacterAccuracy(imageUrl, expectedCharacters);
      validationResult.detailed_scores.character_accuracy = characterScore;

      // 3. 风格匹配检查
      const styleScore = await this.validateStyleMatching(imageUrl, referenceImage);
      validationResult.detailed_scores.style_matching = styleScore;

      // 4. 背景简洁性检查
      const backgroundScore = await this.validateBackgroundSimplicity(imageUrl);
      validationResult.detailed_scores.background_simplicity = backgroundScore;

      // 计算总分
      validationResult.overall_score = this.calculateOverallScore(validationResult.detailed_scores);
      validationResult.is_consistent = validationResult.overall_score >= 0.75;

      // 生成问题和建议
      this.generateIssuesAndSuggestions(validationResult);

      console.log('✅ 风格一致性验证完成');
      console.log('📊 总分:', validationResult.overall_score);
      console.log('✔️ 是否一致:', validationResult.is_consistent);

      return validationResult;
    } catch (error) {
      console.error('❌ 风格一致性验证失败:', error);
      validationResult.issues.push('验证过程中发生错误: ' + error.message);
      return validationResult;
    }
  }

  /**
   * 验证颜色一致性
   */
  async validateColorConsistency(imageUrl) {
    // 模拟颜色分析 - 在实际应用中可以使用图像分析API
    console.log('🎨 检查颜色一致性...');
    
    // 基于预定义的颜色规范进行检查
    const expectedColors = Object.values(ARTISTIC_STYLE_GUIDE.colorPalette).flat();
    
    // 模拟分析结果
    const score = 0.85 + Math.random() * 0.1; // 85-95%的模拟分数
    
    console.log(`🎨 颜色一致性分数: ${(score * 100).toFixed(1)}%`);
    return score;
  }

  /**
   * 验证角色准确性
   */
  async validateCharacterAccuracy(imageUrl, expectedCharacters) {
    console.log('👥 检查角色准确性...');
    
    if (expectedCharacters.length === 0) {
      console.log('⚠️ 没有指定期望角色，跳过角色验证');
      return 0.8; // 默认分数
    }

    // 检查每个期望角色的规范
    let totalScore = 0;
    for (const character of expectedCharacters) {
      const spec = CHARACTER_SPECIFICATIONS[character];
      if (spec) {
        // 模拟角色特征检查
        const characterScore = 0.8 + Math.random() * 0.15; // 80-95%的模拟分数
        totalScore += characterScore;
        console.log(`👤 ${spec.name} 准确性分数: ${(characterScore * 100).toFixed(1)}%`);
      }
    }

    const averageScore = expectedCharacters.length > 0 ? totalScore / expectedCharacters.length : 0.8;
    console.log(`👥 角色准确性平均分数: ${(averageScore * 100).toFixed(1)}%`);
    return averageScore;
  }

  /**
   * 验证风格匹配
   */
  async validateStyleMatching(imageUrl, referenceImage) {
    console.log('🎭 检查风格匹配...');
    
    if (!referenceImage) {
      console.log('⚠️ 没有参考图像，使用基础风格检查');
      return 0.75; // 基础分数
    }

    // 模拟风格匹配分析
    const score = 0.8 + Math.random() * 0.15; // 80-95%的模拟分数
    
    console.log(`🎭 风格匹配分数: ${(score * 100).toFixed(1)}%`);
    return score;
  }

  /**
   * 验证背景简洁性
   */
  async validateBackgroundSimplicity(imageUrl) {
    console.log('🖼️ 检查背景简洁性...');
    
    // 模拟背景复杂度分析
    const score = 0.85 + Math.random() * 0.1; // 85-95%的模拟分数
    
    console.log(`🖼️ 背景简洁性分数: ${(score * 100).toFixed(1)}%`);
    return score;
  }

  /**
   * 计算总体分数
   */
  calculateOverallScore(detailedScores) {
    let totalScore = 0;
    let totalWeight = 0;

    for (const [category, weight] of Object.entries(this.validationRules)) {
      const scoreKey = category.replace(/([A-Z])/g, '_$1').toLowerCase();
      if (detailedScores[scoreKey] !== undefined) {
        totalScore += detailedScores[scoreKey] * weight;
        totalWeight += weight;
      }
    }

    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }

  /**
   * 生成问题和建议
   */
  generateIssuesAndSuggestions(validationResult) {
    const { detailed_scores, overall_score } = validationResult;

    // 检查各项分数并生成相应的问题和建议
    if (detailed_scores.color_consistency < 0.7) {
      validationResult.issues.push('颜色一致性不足');
      validationResult.suggestions.push('确保使用指定的颜色代码，避免偏离预设色彩方案');
    }

    if (detailed_scores.character_accuracy < 0.7) {
      validationResult.issues.push('角色特征不准确');
      validationResult.suggestions.push('检查角色的外观、服装和比例是否符合规范');
    }

    if (detailed_scores.style_matching < 0.7) {
      validationResult.issues.push('风格匹配度低');
      validationResult.suggestions.push('确保绘画技法和线条风格与参考图像一致');
    }

    if (detailed_scores.background_simplicity < 0.7) {
      validationResult.issues.push('背景过于复杂');
      validationResult.suggestions.push('简化背景元素，保持3-4个主要元素即可');
    }

    if (overall_score >= 0.9) {
      validationResult.suggestions.push('图像质量优秀，风格一致性很好');
    } else if (overall_score >= 0.75) {
      validationResult.suggestions.push('图像质量良好，可以考虑微调细节');
    } else {
      validationResult.suggestions.push('建议重新生成图像，注意风格一致性要求');
    }
  }

  /**
   * 生成风格一致性报告
   */
  generateConsistencyReport(validationResults) {
    const report = {
      summary: {
        total_images: validationResults.length,
        consistent_images: validationResults.filter(r => r.is_consistent).length,
        average_score: validationResults.reduce((sum, r) => sum + r.overall_score, 0) / validationResults.length,
        generated_at: new Date().toISOString()
      },
      detailed_analysis: validationResults,
      recommendations: this.generateGlobalRecommendations(validationResults)
    };

    console.log('📊 风格一致性报告生成完成');
    console.log(`📈 一致性率: ${(report.summary.consistent_images / report.summary.total_images * 100).toFixed(1)}%`);
    console.log(`📊 平均分数: ${(report.summary.average_score * 100).toFixed(1)}%`);

    return report;
  }

  /**
   * 生成全局建议
   */
  generateGlobalRecommendations(validationResults) {
    const recommendations = [];
    
    const avgColorScore = validationResults.reduce((sum, r) => sum + (r.detailed_scores.color_consistency || 0), 0) / validationResults.length;
    const avgCharacterScore = validationResults.reduce((sum, r) => sum + (r.detailed_scores.character_accuracy || 0), 0) / validationResults.length;
    
    if (avgColorScore < 0.8) {
      recommendations.push('整体颜色一致性需要改进，建议加强颜色规范的执行');
    }
    
    if (avgCharacterScore < 0.8) {
      recommendations.push('角色准确性需要提升，建议细化角色描述规范');
    }
    
    recommendations.push('定期检查和更新风格指南，确保持续的视觉一致性');
    
    return recommendations;
  }
}

// 创建单例实例
export const styleValidator = new StyleConsistencyValidator();

// 便捷函数
export async function validateImageStyle(imageUrl, expectedCharacters = [], referenceImage = null) {
  return await styleValidator.validateImageConsistency(imageUrl, expectedCharacters, referenceImage);
}
