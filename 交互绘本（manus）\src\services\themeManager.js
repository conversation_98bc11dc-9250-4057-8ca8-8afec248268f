// 绘本主题管理器
// 负责主题选择、切换和配置管理

import { 
  STORYBOOK_THEMES, 
  getCharactersByTheme, 
  getArtStyleByTheme,
  validateThemeAndCharacters 
} from './universalStyleManager.js';

/**
 * 主题管理器类
 */
export class ThemeManager {
  constructor() {
    this.currentTheme = 'friendship_adventure'; // 默认主题
    this.themeHistory = [];
    this.customThemes = new Map();
  }

  /**
   * 获取所有可用主题
   */
  getAvailableThemes() {
    const themes = Object.entries(STORYBOOK_THEMES).map(([key, config]) => ({
      id: key,
      name: config.name,
      description: config.description,
      mainThemes: config.mainThemes,
      targetAge: config.targetAge,
      artStyle: config.artStyle,
      characterCount: config.characters.length,
      environmentCount: config.environments.length
    }));

    // 添加自定义主题
    for (const [key, config] of this.customThemes) {
      themes.push({
        id: key,
        name: config.name,
        description: config.description,
        isCustom: true,
        ...config
      });
    }

    return themes;
  }

  /**
   * 设置当前主题
   */
  setCurrentTheme(themeId) {
    const availableThemes = this.getAvailableThemes();
    const theme = availableThemes.find(t => t.id === themeId);
    
    if (!theme) {
      console.error(`主题 "${themeId}" 不存在`);
      return false;
    }

    // 记录主题历史
    if (this.currentTheme !== themeId) {
      this.themeHistory.push({
        from: this.currentTheme,
        to: themeId,
        timestamp: new Date().toISOString()
      });
    }

    this.currentTheme = themeId;
    console.log(`🎨 切换到主题: ${theme.name}`);
    
    // 触发主题变更事件
    this.onThemeChanged(themeId, theme);
    
    return true;
  }

  /**
   * 获取当前主题
   */
  getCurrentTheme() {
    return this.currentTheme;
  }

  /**
   * 获取当前主题的详细信息
   */
  getCurrentThemeInfo() {
    const themes = this.getAvailableThemes();
    return themes.find(t => t.id === this.currentTheme);
  }

  /**
   * 获取主题的角色列表
   */
  getThemeCharacters(themeId = null) {
    const theme = themeId || this.currentTheme;
    const characters = getCharactersByTheme(theme);
    
    return Object.entries(characters).map(([id, spec]) => ({
      id,
      name: spec.name,
      role: spec.role,
      personality: spec.personality,
      appearance: spec.appearance
    }));
  }

  /**
   * 获取主题的艺术风格
   */
  getThemeArtStyle(themeId = null) {
    const theme = themeId || this.currentTheme;
    return getArtStyleByTheme(theme);
  }

  /**
   * 验证角色是否适用于当前主题
   */
  validateCharactersForCurrentTheme(characters) {
    return validateThemeAndCharacters(this.currentTheme, characters);
  }

  /**
   * 获取主题推荐的角色组合
   */
  getRecommendedCharacterCombinations(themeId = null) {
    const theme = themeId || this.currentTheme;
    const themeConfig = STORYBOOK_THEMES[theme];
    
    if (!themeConfig) return [];

    // 根据主题返回推荐的角色组合
    const combinations = {
      friendship_adventure: [
        { name: "主角双人组", characters: ["bobo", "lili"], description: "经典友谊组合" },
        { name: "学习小组", characters: ["bobo", "lili", "teacher"], description: "包含指导者的学习场景" },
        { name: "完整团队", characters: ["bobo", "lili", "teacher", "squirrels"], description: "所有角色的大团队" },
        { name: "活泼组合", characters: ["bobo", "squirrels"], description: "充满活力的冒险组合" }
      ],
      family_life: [
        { name: "核心家庭", characters: ["child", "mom", "dad"], description: "温馨的三口之家" },
        { name: "大家庭", characters: ["child", "mom", "dad", "grandparents"], description: "包含祖辈的大家庭" },
        { name: "亲子时光", characters: ["child", "mom"], description: "母子/母女互动" },
        { name: "父子活动", characters: ["child", "dad"], description: "父子/父女活动" }
      ],
      legal_awareness: [
        { name: "学习组合", characters: ["student", "teacher"], description: "课堂学习场景" },
        { name: "社会教育", characters: ["student", "police"], description: "法律教育场景" },
        { name: "公民教育", characters: ["student", "police", "judge"], description: "完整的法律教育" }
      ],
      moral_character: [
        { name: "榜样示范", characters: ["hero"], description: "品格榜样展示" },
        { name: "互助场景", characters: ["hero", "helper"], description: "助人为乐场景" },
        { name: "社区服务", characters: ["hero", "community"], description: "社区服务场景" }
      ]
    };

    return combinations[theme] || [];
  }

  /**
   * 添加自定义主题
   */
  addCustomTheme(themeId, themeConfig) {
    // 验证主题配置
    const requiredFields = ['name', 'description', 'mainThemes', 'targetAge', 'artStyle'];
    for (const field of requiredFields) {
      if (!themeConfig[field]) {
        throw new Error(`自定义主题缺少必需字段: ${field}`);
      }
    }

    this.customThemes.set(themeId, {
      ...themeConfig,
      isCustom: true,
      createdAt: new Date().toISOString()
    });

    console.log(`✅ 添加自定义主题: ${themeConfig.name}`);
    return true;
  }

  /**
   * 删除自定义主题
   */
  removeCustomTheme(themeId) {
    if (!this.customThemes.has(themeId)) {
      console.error(`自定义主题 "${themeId}" 不存在`);
      return false;
    }

    // 如果当前使用的是要删除的主题，切换到默认主题
    if (this.currentTheme === themeId) {
      this.setCurrentTheme('friendship_adventure');
    }

    this.customThemes.delete(themeId);
    console.log(`🗑️ 删除自定义主题: ${themeId}`);
    return true;
  }

  /**
   * 获取主题使用统计
   */
  getThemeUsageStats() {
    const stats = {};
    
    // 统计主题切换历史
    for (const record of this.themeHistory) {
      stats[record.to] = (stats[record.to] || 0) + 1;
    }

    // 添加当前主题
    stats[this.currentTheme] = (stats[this.currentTheme] || 0) + 1;

    return {
      currentTheme: this.currentTheme,
      usageCount: stats,
      totalSwitches: this.themeHistory.length,
      mostUsedTheme: Object.entries(stats).sort(([,a], [,b]) => b - a)[0]?.[0]
    };
  }

  /**
   * 导出主题配置
   */
  exportThemeConfig() {
    return {
      currentTheme: this.currentTheme,
      themeHistory: this.themeHistory,
      customThemes: Object.fromEntries(this.customThemes),
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * 导入主题配置
   */
  importThemeConfig(config) {
    try {
      if (config.currentTheme) {
        this.currentTheme = config.currentTheme;
      }
      
      if (config.themeHistory) {
        this.themeHistory = config.themeHistory;
      }
      
      if (config.customThemes) {
        this.customThemes = new Map(Object.entries(config.customThemes));
      }

      console.log('✅ 主题配置导入成功');
      return true;
    } catch (error) {
      console.error('❌ 主题配置导入失败:', error);
      return false;
    }
  }

  /**
   * 主题变更回调 (可被重写)
   */
  onThemeChanged(themeId, themeInfo) {
    // 子类可以重写此方法来处理主题变更事件
    console.log(`🎨 主题已变更为: ${themeInfo.name}`);
  }

  /**
   * 重置到默认主题
   */
  resetToDefault() {
    this.setCurrentTheme('friendship_adventure');
    this.themeHistory = [];
    this.customThemes.clear();
    console.log('🔄 已重置到默认主题配置');
  }
}

// 创建全局主题管理器实例
export const themeManager = new ThemeManager();

// 便捷函数
export function getCurrentTheme() {
  return themeManager.getCurrentTheme();
}

export function setTheme(themeId) {
  return themeManager.setCurrentTheme(themeId);
}

export function getThemeCharacters(themeId = null) {
  return themeManager.getThemeCharacters(themeId);
}

export function getAvailableThemes() {
  return themeManager.getAvailableThemes();
}

export function getRecommendedCharacters(themeId = null) {
  return themeManager.getRecommendedCharacterCombinations(themeId);
}
