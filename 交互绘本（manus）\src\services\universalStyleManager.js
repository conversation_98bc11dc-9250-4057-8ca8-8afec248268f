// 通用风格一致性管理器
// 支持多个绘本主题和角色系统的可扩展架构

/**
 * 绘本主题配置
 */
export const STORYBOOK_THEMES = {
  // 小熊波波的友谊冒险系列
  friendship_adventure: {
    name: "小熊波波的友谊冒险",
    description: "以友谊、合作、成长为主题的温馨故事",
    mainThemes: ["人际关系", "友谊", "合作"],
    targetAge: "6-8岁",
    artStyle: "温暖水彩画风格",
    characters: ["bobo", "lili", "teacher", "squirrels"],
    environments: ["forest", "school", "playground", "home"]
  },

  // 家庭生活系列
  family_life: {
    name: "温馨家庭故事",
    description: "展现家庭温暖和亲情的日常生活故事",
    mainThemes: ["家庭生活", "亲情", "责任"],
    targetAge: "5-7岁",
    artStyle: "柔和插画风格",
    characters: ["child", "mom", "dad", "grandparents", "pet"],
    environments: ["home", "kitchen", "garden", "bedroom"]
  },

  // 法律常识系列
  legal_awareness: {
    name: "小小公民成长记",
    description: "用简单故事教导基本法律常识和社会规则",
    mainThemes: ["法律常识", "规则意识", "安全教育"],
    targetAge: "7-9岁",
    artStyle: "清晰教育插画风格",
    characters: ["student", "police", "teacher", "judge", "citizen"],
    environments: ["school", "court", "street", "park", "community"]
  },

  // 道德品格系列
  moral_character: {
    name: "品格小故事",
    description: "培养良好品格和道德观念的教育故事",
    mainThemes: ["人伦道德", "品格教育", "价值观"],
    targetAge: "6-8岁",
    artStyle: "温馨教育插画风格",
    characters: ["hero", "helper", "elder", "friend", "community"],
    environments: ["community", "school", "home", "public_space"]
  }
};

/**
 * 通用角色规范系统
 */
export const UNIVERSAL_CHARACTER_SYSTEM = {
  // 小熊波波友谊冒险系列角色
  friendship_adventure: {
    bobo: {
      name: "小熊波波 (Bear Bobo)",
      role: "主角",
      personality: "友善、好奇、勇敢",
      appearance: {
        species: "棕熊",
        furColor: "#D2691E", // 焦糖棕色
        eyeColor: "#000000",
        clothing: {
          top: { color: "#DC143C", style: "圆领短袖T恤" },
          bottom: { color: "#191970", style: "短裤" }
        },
        accessories: [],
        height: "画面高度的1/3",
        bodyType: "略微圆润",
        facialFeatures: "圆脸、大眼睛、小圆鼻子"
      }
    },
    lili: {
      name: "小兔莉莉 (Rabbit Lily)",
      role: "主要配角",
      personality: "温柔、善良、细心",
      appearance: {
        species: "兔子",
        furColor: "#F8F8FF", // 珍珠白
        eyeColor: "#000000",
        clothing: {
          dress: { color: "#FFB6C1", style: "连衣裙" }
        },
        accessories: [{ type: "发饰", color: "#FFD700", description: "黄色小花" }],
        height: "比波波小10%",
        bodyType: "纤细优雅",
        facialFeatures: "椭圆脸、长耳朵、三角鼻子"
      }
    },
    teacher: {
      name: "乌龟老师 (Turtle Teacher)",
      role: "智慧引导者",
      personality: "智慧、耐心、可靠",
      appearance: {
        species: "乌龟",
        shellColor: "#8B4513", // 深棕色
        skinColor: "#808000", // 橄榄绿
        eyeColor: "#000000",
        accessories: [{ type: "眼镜", color: "#FFD700", description: "金色圆框眼镜" }],
        height: "中等身材",
        bodyType: "稳重",
        facialFeatures: "方脸、智慧表情"
      }
    },
    squirrels: {
      name: "松鼠兄弟 (Squirrel Brothers)",
      role: "活泼配角",
      personality: "活泼、机灵、好动",
      appearance: {
        species: "松鼠",
        furColor: "#A0522D", // 红棕色
        eyeColor: "#000000",
        clothing: {
          brother1: { color: "#FFD700", style: "背心" },
          brother2: { color: "#32CD32", style: "背心" }
        },
        height: "波波的2/3",
        bodyType: "小巧灵活",
        facialFeatures: "尖脸、蓬松尾巴"
      }
    }
  },

  // 家庭生活系列角色
  family_life: {
    child: {
      name: "小主角",
      role: "家庭中的孩子",
      personality: "好奇、活泼、爱学习",
      appearance: {
        species: "人类儿童",
        hairColor: "#8B4513",
        eyeColor: "#654321",
        clothing: {
          casual: { colors: ["#87CEEB", "#FFB6C1", "#90EE90"] }
        },
        height: "儿童比例",
        bodyType: "活泼可爱"
      }
    },
    mom: {
      name: "妈妈",
      role: "温暖的母亲",
      personality: "温柔、关爱、耐心",
      appearance: {
        species: "人类成人",
        hairColor: "#654321",
        eyeColor: "#8B4513",
        clothing: {
          home: { colors: ["#DDA0DD", "#F0E68C"] }
        },
        height: "成人比例",
        bodyType: "温和优雅"
      }
    },
    dad: {
      name: "爸爸",
      role: "可靠的父亲",
      personality: "稳重、幽默、负责",
      appearance: {
        species: "人类成人",
        hairColor: "#696969",
        eyeColor: "#8B4513",
        clothing: {
          casual: { colors: ["#4682B4", "#228B22"] }
        },
        height: "成人比例",
        bodyType: "稳重可靠"
      }
    }
  },

  // 法律常识系列角色
  legal_awareness: {
    student: {
      name: "小学生",
      role: "学习者",
      personality: "好学、守规则、有正义感",
      appearance: {
        species: "人类儿童",
        clothing: {
          school: { colors: ["#000080", "#FFFFFF"] }
        }
      }
    },
    police: {
      name: "警察叔叔",
      role: "法律守护者",
      personality: "正义、友善、专业",
      appearance: {
        species: "人类成人",
        clothing: {
          uniform: { colors: ["#000080", "#C0C0C0"] }
        }
      }
    }
  },

  // 道德品格系列角色
  moral_character: {
    hero: {
      name: "小英雄",
      role: "品格榜样",
      personality: "勇敢、善良、正直",
      appearance: {
        species: "可变动物或人类",
        clothing: {
          colors: ["#FFD700", "#32CD32", "#87CEEB"]
        }
      }
    }
  }
};

/**
 * 通用艺术风格规范
 */
export const UNIVERSAL_ART_STYLES = {
  warm_watercolor: {
    name: "温暖水彩画风格",
    description: "适合友谊和家庭主题",
    technique: "水彩画技法，轻微纸张纹理",
    lineStyle: "深棕色轮廓线，2-3px宽度",
    colorPalette: {
      primary: ["#D2691E", "#CD853F", "#DEB887"],
      secondary: ["#90EE90", "#98FB98", "#87CEEB", "#B0E0E6"],
      accent: ["#DC143C", "#FFB6C1", "#FFD700"]
    },
    lighting: "柔和自然光，左上45度角",
    background: "简洁清爽，最多3-4元素"
  },

  educational_clear: {
    name: "清晰教育插画风格",
    description: "适合法律和道德教育主题",
    technique: "清晰线条插画，适度阴影",
    lineStyle: "黑色轮廓线，3-4px宽度",
    colorPalette: {
      primary: ["#4682B4", "#2E8B57", "#B8860B"],
      secondary: ["#87CEEB", "#98FB98", "#F0E68C"],
      accent: ["#FF6347", "#9370DB", "#32CD32"]
    },
    lighting: "均匀明亮光线",
    background: "结构化，支持教育内容"
  },

  soft_pastel: {
    name: "柔和粉彩风格",
    description: "适合温馨家庭主题",
    technique: "柔和粉彩技法，温暖色调",
    lineStyle: "浅棕色轮廓线，1-2px宽度",
    colorPalette: {
      primary: ["#DDA0DD", "#F0E68C", "#E6E6FA"],
      secondary: ["#FFE4E1", "#F0FFF0", "#F5F5DC"],
      accent: ["#FF69B4", "#FFA500", "#9ACD32"]
    },
    lighting: "温暖柔和光线",
    background: "温馨舒适，家庭氛围"
  }
};

/**
 * 环境场景规范
 */
export const UNIVERSAL_ENVIRONMENTS = {
  forest: {
    name: "森林环境",
    elements: {
      trees: { color: "#228B22", description: "圆形树冠，棕色树干" },
      grass: { color: "#90EE90", description: "嫩绿草地，小花点缀" },
      path: { color: "#D2B48C", description: "自然土路" },
      sky: { color: "#E6F3FF", description: "淡蓝天空，白云" }
    }
  },
  home: {
    name: "家庭环境",
    elements: {
      walls: { color: "#F5DEB3", description: "温暖米色墙面" },
      furniture: { colors: ["#8B4513", "#CD853F"], description: "木质家具" },
      decorations: { colors: ["#FFB6C1", "#87CEEB"], description: "温馨装饰" },
      lighting: { description: "温暖室内光线" }
    }
  },
  school: {
    name: "学校环境",
    elements: {
      classroom: { colors: ["#F0F8FF", "#FFFACD"], description: "明亮教室" },
      blackboard: { color: "#2F4F4F", description: "深色黑板" },
      desks: { color: "#DEB887", description: "学习桌椅" },
      books: { colors: ["#FF6347", "#32CD32", "#4169E1"], description: "彩色书籍" }
    }
  }
};

/**
 * 根据主题获取角色规范
 */
export function getCharactersByTheme(theme) {
  const themeConfig = STORYBOOK_THEMES[theme];
  if (!themeConfig) {
    console.warn(`未找到主题 "${theme}" 的配置，使用默认主题`);
    return UNIVERSAL_CHARACTER_SYSTEM.friendship_adventure;
  }
  
  return UNIVERSAL_CHARACTER_SYSTEM[theme] || UNIVERSAL_CHARACTER_SYSTEM.friendship_adventure;
}

/**
 * 根据主题获取艺术风格
 */
export function getArtStyleByTheme(theme) {
  const styleMap = {
    friendship_adventure: "warm_watercolor",
    family_life: "soft_pastel",
    legal_awareness: "educational_clear",
    moral_character: "warm_watercolor"
  };
  
  const styleName = styleMap[theme] || "warm_watercolor";
  return UNIVERSAL_ART_STYLES[styleName];
}

/**
 * 生成通用的LiblibAI提示词
 */
export function generateUniversalPrompt(sceneDescription, theme, characters = [], ageRange = "6-8岁") {
  const themeConfig = STORYBOOK_THEMES[theme] || STORYBOOK_THEMES.friendship_adventure;
  const characterSpecs = getCharactersByTheme(theme);
  const artStyle = getArtStyleByTheme(theme);
  
  // 构建角色描述
  const characterDescs = characters.map(charId => {
    const char = characterSpecs[charId];
    if (!char) return "";
    
    return `${char.name}: ${char.appearance.species}，${char.personality}特质，${JSON.stringify(char.appearance)}`;
  }).filter(desc => desc).join("\n");
  
  // 构建完整提示词
  const prompt = `
Create a ${artStyle.description} children's book illustration for "${themeConfig.name}" series:

SCENE: ${sceneDescription}

THEME CONTEXT: ${themeConfig.description}
TARGET AGE: ${ageRange} autistic children

CHARACTER SPECIFICATIONS:
${characterDescs || "使用主题默认角色"}

ARTISTIC STYLE - ${artStyle.name}:
- Technique: ${artStyle.technique}
- Line Style: ${artStyle.lineStyle}
- Color Palette: ${JSON.stringify(artStyle.colorPalette)}
- Lighting: ${artStyle.lighting}
- Background: ${artStyle.background}

CONSISTENCY REQUIREMENTS:
1. Maintain exact character appearance as specified
2. Use only the defined color palette
3. Follow the artistic style guidelines precisely
4. Ensure age-appropriate content for autistic children
5. Keep background elements simple and supportive

Generate a wholesome, educational illustration that perfectly matches the "${themeConfig.name}" series visual style.`;

  return prompt.trim();
}

/**
 * 验证主题和角色的有效性
 */
export function validateThemeAndCharacters(theme, characters) {
  const themeConfig = STORYBOOK_THEMES[theme];
  if (!themeConfig) {
    return {
      valid: false,
      error: `未知主题: ${theme}`,
      availableThemes: Object.keys(STORYBOOK_THEMES)
    };
  }
  
  const characterSpecs = getCharactersByTheme(theme);
  const invalidCharacters = characters.filter(char => !characterSpecs[char]);
  
  if (invalidCharacters.length > 0) {
    return {
      valid: false,
      error: `主题 "${theme}" 中不存在角色: ${invalidCharacters.join(", ")}`,
      availableCharacters: Object.keys(characterSpecs)
    };
  }
  
  return { valid: true };
}
